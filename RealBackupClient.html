<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Encrypted Backup Client</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.2em;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .header .subtitle {
            color: #666;
            font-size: 1.1em;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            margin-left: 10px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-indicator.connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status-indicator.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 1.05em;
        }
        
        .form-group input[type="text"],
        .form-group input[type="file"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }
        
        .form-group input[type="text"]:focus,
        .form-group input[type="file"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .server-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border-left: 4px solid #667eea;
        }
        
        .server-info h3 {
            color: #333;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .server-info p {
            color: #666;
            margin: 4px 0;
        }
        
        .backup-button {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        
        .backup-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .backup-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        .progress-container {
            margin-top: 24px;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 16px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
            border-radius: 4px;
        }
        
        .status-log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            line-height: 1.4;
        }
        
        .result-container {
            margin-top: 24px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        
        .result-container.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result-container.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .verification-details {
            margin-top: 16px;
            padding: 16px;
            background: rgba(0,0,0,0.05);
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .file-upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            transition: border-color 0.3s ease;
            cursor: pointer;
        }
        
        .file-upload-area:hover {
            border-color: #667eea;
        }
        
        .file-upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Real Encrypted Backup</h1>
            <p class="subtitle">ACTUAL file backup with verification</p>
            <span id="serverStatus" class="status-indicator disconnected">Checking server...</span>
        </div>
        
        <div class="server-info">
            <h3>Server Information</h3>
            <p><strong>Address:</strong> 127.0.0.1:1256</p>
            <p><strong>Encryption:</strong> RSA-1024 + AES-256-CBC</p>
            <p><strong>Protocol:</strong> Binary TCP with CRC verification</p>
        </div>
        
        <form id="backupForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" placeholder="Enter your username" required>
            </div>
            
            <div class="form-group">
                <label for="fileInput">File to Backup:</label>
                <div class="file-upload-area" id="fileUploadArea">
                    <input type="file" id="fileInput" name="file" style="display: none;" required>
                    <p>Click here or drag & drop a file to backup</p>
                </div>
                <div id="fileInfo" style="margin-top: 12px; color: #666; font-size: 0.9em;"></div>
            </div>
            
            <button type="submit" class="backup-button" id="backupButton">
                Start REAL Backup
            </button>
        </form>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="status-log" id="statusLog"></div>
        </div>
        
        <div class="result-container" id="resultContainer">
            <h3 id="resultTitle"></h3>
            <p id="resultMessage"></p>
            <div class="verification-details" id="verificationDetails"></div>
        </div>
    </div>

    <script>
        let currentBackupProcess = null;
        
        // File upload handling
        const fileInput = document.getElementById('fileInput');
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInfo = document.getElementById('fileInfo');
        
        fileUploadArea.addEventListener('click', () => fileInput.click());
        
        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });
        
        fileUploadArea.addEventListener('dragleave', () => {
            fileUploadArea.classList.remove('dragover');
        });
        
        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                displayFileInfo(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                displayFileInfo(e.target.files[0]);
            }
        });
        
        function displayFileInfo(file) {
            const size = (file.size / 1024).toFixed(2);
            fileInfo.innerHTML = `Selected: <strong>${file.name}</strong> (${size} KB)`;
        }
        
        // Server status check
        async function checkServerStatus() {
            try {
                const response = await fetch('/api/server-status');
                const data = await response.json();
                const statusElement = document.getElementById('serverStatus');
                
                if (data.running) {
                    statusElement.textContent = `Server Online (${data.clients} clients)`;
                    statusElement.className = 'status-indicator connected';
                } else {
                    statusElement.textContent = 'Server Offline';
                    statusElement.className = 'status-indicator disconnected';
                }
            } catch (error) {
                document.getElementById('serverStatus').textContent = 'Connection Error';
                document.getElementById('serverStatus').className = 'status-indicator disconnected';
            }
        }
        
        // Backup form submission
        document.getElementById('backupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const file = document.getElementById('fileInput').files[0];
            
            if (!file) {
                alert('Please select a file to backup');
                return;
            }
            
            await startRealBackup(username, file);
        });
        
        async function startRealBackup(username, file) {
            const backupButton = document.getElementById('backupButton');
            const progressContainer = document.getElementById('progressContainer');
            const resultContainer = document.getElementById('resultContainer');
            const statusLog = document.getElementById('statusLog');
            
            // Reset UI
            backupButton.disabled = true;
            backupButton.innerHTML = '<span class="spinner"></span>Starting backup...';
            progressContainer.style.display = 'block';
            resultContainer.style.display = 'none';
            statusLog.textContent = '';
            
            try {
                // Upload file first
                const formData = new FormData();
                formData.append('username', username);
                formData.append('file', file);
                
                logStatus('UPLOAD', 'Uploading file to backup server...');
                
                const response = await fetch('/api/backup', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                // Start monitoring backup progress
                const result = await response.json();
                
                if (result.success) {
                    showResult(true, 'Backup Completed Successfully!', 
                              'Your file has been securely encrypted and transferred.', result);
                } else {
                    showResult(false, 'Backup Failed', result.error || 'Unknown error occurred', result);
                }
                
            } catch (error) {
                logStatus('ERROR', `Backup failed: ${error.message}`);
                showResult(false, 'Backup Failed', error.message);
            } finally {
                backupButton.disabled = false;
                backupButton.innerHTML = 'Start REAL Backup';
            }
        }
        
        function logStatus(phase, message) {
            const statusLog = document.getElementById('statusLog');
            const timestamp = new Date().toLocaleTimeString();
            statusLog.textContent += `[${timestamp}] ${phase}: ${message}\n`;
            statusLog.scrollTop = statusLog.scrollHeight;
        }
        
        function showResult(success, title, message, details = null) {
            const resultContainer = document.getElementById('resultContainer');
            const resultTitle = document.getElementById('resultTitle');
            const resultMessage = document.getElementById('resultMessage');
            const verificationDetails = document.getElementById('verificationDetails');
            
            resultContainer.className = `result-container ${success ? 'success' : 'error'}`;
            resultTitle.textContent = title;
            resultMessage.textContent = message;
            
            if (details && details.verification) {
                const v = details.verification;
                verificationDetails.innerHTML = `
                    File Transfer Verification:
                    • File Found: ${v.file_found ? '✅' : '❌'}
                    • Size Match: ${v.size_match ? '✅' : '❌'} (${v.original_size} → ${v.received_size} bytes)
                    • Hash Match: ${v.hash_match ? '✅' : '❌'}
                    • Transferred: ${v.transferred ? '✅' : '❌'}
                    ${v.received_file ? `• Received File: ${v.received_file}` : ''}
                    
                    Process Details:
                    • Exit Code: ${details.process_exit_code}
                    • Duration: ${details.duration.toFixed(2)}s
                    • Network Activity: ${details.network_activity ? '✅' : '❌'}
                `;
                verificationDetails.style.display = 'block';
            } else {
                verificationDetails.style.display = 'none';
            }
            
            resultContainer.style.display = 'block';
        }
        
        // Initialize page
        checkServerStatus();
        setInterval(checkServerStatus, 30000); // Check every 30 seconds
    </script>
</body>
</html>
