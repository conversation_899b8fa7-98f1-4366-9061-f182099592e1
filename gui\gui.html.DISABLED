<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Backup Client - Ultra Modern GUI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-bg: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(255, 255, 255, 0.15);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --accent-green: #00d4aa;
            --accent-blue: #4facfe;
            --accent-orange: #ff9a56;
            --accent-red: #ff6b6b;
            --shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Se<PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated background particles */
        .bg-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Glass morphism container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            grid-template-rows: auto 1fr auto;
            gap: 20px;
            grid-template-areas: 
                "header header header"
                "config main status"
                "footer footer footer";
        }

        /* Header */
        .header {
            grid-area: header;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 20px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, var(--accent-green), var(--accent-blue));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .logo-text h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .logo-text p {
            font-size: 12px;
            color: var(--text-secondary);
            letter-spacing: 1px;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--accent-red);
            position: relative;
            transition: var(--transition);
        }

        .status-indicator.connected {
            background: var(--accent-green);
            animation: connected-pulse 2s ease-in-out infinite;
        }

        @keyframes connected-pulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(0, 212, 170, 0.7); }
            50% { box-shadow: 0 0 0 10px rgba(0, 212, 170, 0); }
        }

        /* Configuration Panel */
        .config-panel {
            grid-area: config;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 25px;
            height: fit-content;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-title::before {
            content: "⚙️";
            font-size: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-secondary);
        }

        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px 15px;
            color: var(--text-primary);
            font-size: 14px;
            transition: var(--transition);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .file-drop-zone {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .file-drop-zone:hover {
            border-color: var(--accent-blue);
            background: rgba(79, 172, 254, 0.1);
        }

        .file-drop-zone.active {
            border-color: var(--accent-green);
            background: rgba(0, 212, 170, 0.1);
        }

        .file-info {
            display: none;
            padding: 15px;
            background: rgba(0, 212, 170, 0.1);
            border-radius: 8px;
            margin-top: 10px;
        }

        .file-info.show {
            display: block;
        }

        /* Main Transfer Area */
        .main-panel {
            grid-area: main;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        .progress-circle {
            width: 250px;
            height: 250px;
            position: relative;
            margin-bottom: 30px;
        }

        .progress-ring {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .progress-ring circle {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
        }

        .progress-bg {
            stroke: rgba(255, 255, 255, 0.2);
        }

        .progress-fill {
            stroke: url(#progressGradient);
            stroke-dasharray: 628;
            stroke-dashoffset: 628;
            transition: stroke-dashoffset 0.5s ease;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 36px;
            font-weight: 700;
        }

        .progress-label {
            position: absolute;
            top: 60%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            color: var(--text-secondary);
        }

        .transfer-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            width: 100%;
            margin-top: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .phase-indicator {
            margin-bottom: 20px;
            padding: 10px 20px;
            background: rgba(79, 172, 254, 0.2);
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        /* Status Panel */
        .status-panel {
            grid-area: status;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 25px;
            height: fit-content;
            max-height: 500px;
            overflow-y: auto;
        }

        .status-log {
            max-height: 300px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.5;
        }

        .log-entry {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .log-timestamp {
            color: var(--text-secondary);
            font-size: 11px;
        }

        .log-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .log-status.success { background: var(--accent-green); }
        .log-status.error { background: var(--accent-red); }
        .log-status.info { background: var(--accent-blue); }

        .security-status {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 212, 170, 0.1);
            border-radius: 8px;
        }

        .security-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .security-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .security-badge.active {
            background: var(--accent-green);
            color: white;
        }

        .security-badge.inactive {
            background: rgba(255, 255, 255, 0.2);
            color: var(--text-secondary);
        }

        /* Footer */
        .footer {
            grid-area: footer;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 20px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .client-info {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--accent-green), var(--accent-blue));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 212, 170, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr 1fr;
                grid-template-areas: 
                    "header header"
                    "config main"
                    "status status"
                    "footer footer";
            }
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-areas: 
                    "header"
                    "config"
                    "main"
                    "status"
                    "footer";
                padding: 10px;
                gap: 15px;
            }
            
            .progress-circle {
                width: 200px;
                height: 200px;
            }
            
            .transfer-stats {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }

        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        /* Error animations */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        /* Success animations */
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: scale(1); }
            40%, 43% { transform: scale(1.1); }
            70% { transform: scale(1.05); }
        }

        .bounce {
            animation: bounce 1s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="bg-particles" id="particles"></div>
    
    <div class="container">
        <!-- Header -->
        <header class="header slide-in">
            <div class="logo">
                <div class="logo-icon">🛡️</div>
                <div class="logo-text">
                    <h1>SecureBackup</h1>
                    <p>ULTRA MODERN CLIENT</p>
                </div>
            </div>
            <div class="connection-status">
                <div class="status-indicator" id="statusIndicator"></div>
                <span id="connectionStatus">Monitoring...</span>
            </div>
        </header>

        <!-- Configuration Panel -->
        <aside class="config-panel slide-in">
            <h2 class="panel-title">Configuration</h2>
            
            <div class="form-group">
                <label class="form-label">Server Address</label>
                <input type="text" class="form-input" id="serverIP" placeholder="*************" value="127.0.0.1" readonly>
            </div>
            
            <div class="form-group">
                <label class="form-label">Port</label>
                <input type="number" class="form-input" id="serverPort" placeholder="1234" value="1234" readonly>
            </div>
            
            <div class="form-group">
                <label class="form-label">Username</label>
                <input type="text" class="form-input" id="username" placeholder="Enter username" value="testuser" readonly>
            </div>
            
            <div class="form-group">
                <label class="form-label">File Status</label>
                <div class="file-drop-zone">
                    <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
                    <p><strong>File monitoring active</strong></p>
                    <p style="font-size: 12px; color: var(--text-secondary); margin-top: 5px;">
                        Configured via transfer.info
                    </p>
                </div>
                <div class="file-info show" id="fileInfo">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span>📄</span>
                        <div>
                            <div id="fileName">Waiting for file info...</div>
                            <div id="fileSize" style="font-size: 12px; color: var(--text-secondary);">Size: Unknown</div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Transfer Area -->
        <main class="main-panel slide-in">
            <div class="phase-indicator" id="phaseIndicator">🚀 Monitoring Client Activity</div>
            
            <div class="progress-circle">
                <svg class="progress-ring" viewBox="0 0 200 200">
                    <defs>
                        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#00d4aa;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#4facfe;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <circle class="progress-bg" cx="100" cy="100" r="90"></circle>
                    <circle class="progress-fill" cx="100" cy="100" r="90" id="progressRing"></circle>
                </svg>
                <div class="progress-text" id="progressText">0%</div>
                <div class="progress-label" id="progressLabel">Ready</div>
            </div>
            
            <div class="transfer-stats">
                <div class="stat-card">
                    <div class="stat-value" id="speedValue">0 MB/s</div>
                    <div class="stat-label">Current Speed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="etaValue">--</div>
                    <div class="stat-label">Time Remaining</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="transferredValue">0 MB</div>
                    <div class="stat-label">Transferred</div>
                </div>
            </div>
        </main>

        <!-- Status Panel -->
        <aside class="status-panel slide-in">
            <h2 class="panel-title">Status Monitor</h2>
            
            <div class="status-log" id="statusLog">
                <div class="log-entry">
                    <span class="log-timestamp">00:00:00</span>
                    <div class="log-status info"></div>
                    <span>GUI loaded - monitoring client activity...</span>
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">00:00:01</span>
                    <div class="log-status info"></div>
                    <span>Waiting for status updates from client...</span>
                </div>
            </div>
            
            <div class="security-status">
                <h3 style="font-size: 14px; margin-bottom: 10px;">🔐 Security Status</h3>
                <div class="security-item">
                    <span>RSA-1024 Keys</span>
                    <span class="security-badge inactive" id="rsaStatus">Pending</span>
                </div>
                <div class="security-item">
                    <span>AES-256 Encryption</span>
                    <span class="security-badge inactive" id="aesStatus">Pending</span>
                </div>
                <div class="security-item">
                    <span>Secure Connection</span>
                    <span class="security-badge inactive" id="connectionSecurity">Pending</span>
                </div>
                <div class="security-item">
                    <span>File Integrity</span>
                    <span class="security-badge inactive" id="integrityStatus">Pending</span>
                </div>
            </div>
        </aside>

        <!-- Footer -->
        <footer class="footer slide-in">
            <div class="client-info">
                <div>Client ID: <span id="clientId">Not registered</span></div>
                <div style="margin-top: 4px;">Protocol Version: 3 | Build: 2025.06.14</div>
            </div>
            <div class="action-buttons">
                <button class="btn btn-secondary" id="refreshBtn">Refresh Status</button>
                <button class="btn btn-secondary" id="connectBtn">Connect to Server</button>
                <button class="btn btn-primary" id="monitorBtn">Live Monitor: ON</button>
            </div>
        </footer>
    </div>

    <script>
        // Live monitoring for file-based integration
        class StatusMonitor {
            constructor() {
                this.lastLineCount = 0;
                this.isMonitoring = true;
                this.currentProgress = 0;
                this.currentPhase = 'Ready';
                
                this.initializeEventListeners();
                this.createBackgroundParticles();
                this.startMonitoring();
            }

            initializeEventListeners() {
                document.getElementById('refreshBtn').addEventListener('click', () => {
                    this.refreshStatus();
                });

                document.getElementById('connectBtn').addEventListener('click', () => {
                    this.toggleConnection();
                });

                document.getElementById('monitorBtn').addEventListener('click', () => {
                    this.toggleMonitoring();
                });
            }

            createBackgroundParticles() {
                const container = document.getElementById('particles');
                for (let i = 0; i < 20; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = Math.random() * 100 + '%';
                    particle.style.width = (Math.random() * 10 + 5) + 'px';
                    particle.style.height = particle.style.width;
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                    container.appendChild(particle);
                }
            }

            startMonitoring() {
                if (!this.isMonitoring) return;
                
                // Check for status updates every second
                setInterval(() => {
                    if (this.isMonitoring) {
                        this.checkForUpdates();
                    }
                }, 1000);
                
                this.addLogEntry('Live monitoring started', 'info');
            }

            async checkForUpdates() {
                try {
                    // Try to read the status file created by C++ client
                    const response = await fetch('gui_status.json?' + Date.now());
                    if (response.ok) {
                        const text = await response.text();
                        this.processStatusFile(text);
                    }
                } catch (e) {
                    // File doesn't exist yet - that's okay
                }

                // Also check for progress file
                try {
                    const progressResponse = await fetch('gui_progress.json?' + Date.now());
                    if (progressResponse.ok) {
                        const progressData = await progressResponse.json();
                        this.updateProgress(progressData);
                    }
                } catch (e) {
                    // Progress file doesn't exist yet
                }
            }

            processStatusFile(text) {
                if (!text.trim()) return;
                
                const lines = text.trim().split('\n').filter(line => line.length > 0);
                
                if (lines.length > this.lastLineCount) {
                    for (let i = this.lastLineCount; i < lines.length; i++) {
                        try {
                            const data = JSON.parse(lines[i]);
                            this.handleStatusUpdate(data);
                        } catch (e) {
                            // Invalid JSON line, skip it
                        }
                    }
                    this.lastLineCount = lines.length;
                }
            }

            handleStatusUpdate(data) {
                // Update connection status
                if (data.operation.toLowerCase().includes('connect')) {
                    this.updateConnectionStatus(data.success);
                }
                
                // Update security badges
                if (data.operation.toLowerCase().includes('rsa')) {
                    this.updateSecurityStatus('rsaStatus', data.success);
                }
                if (data.operation.toLowerCase().includes('aes')) {
                    this.updateSecurityStatus('aesStatus', data.success);
                }
                if (data.operation.toLowerCase().includes('encryption')) {
                    this.updateSecurityStatus('connectionSecurity', data.success);
                }
                if (data.operation.toLowerCase().includes('crc') || data.operation.toLowerCase().includes('integrity')) {
                    this.updateSecurityStatus('integrityStatus', data.success);
                }
                
                // Update phase
                if (data.operation.toLowerCase().includes('phase') || data.operation.toLowerCase().includes('connection setup')) {
                    this.updatePhase('🔗 ' + data.operation);
                } else if (data.operation.toLowerCase().includes('auth')) {
                    this.updatePhase('🔐 ' + data.operation);
                } else if (data.operation.toLowerCase().includes('transfer')) {
                    this.updatePhase('📤 ' + data.operation);
                } else if (data.operation.toLowerCase().includes('complete')) {
                    this.updatePhase('✅ ' + data.operation);
                }
                
                // Add to log
                this.addLogEntry(`${data.operation}: ${data.details}`, data.success ? 'success' : 'error');
            }

            updateProgress(data) {
                if (data.percentage !== undefined) {
                    this.currentProgress = data.percentage;
                    this.setProgress(data.percentage);
                }
                
                if (data.speed) {
                    document.getElementById('speedValue').textContent = data.speed;
                }
                
                if (data.eta) {
                    document.getElementById('etaValue').textContent = data.eta;
                }
                
                if (data.transferred) {
                    document.getElementById('transferredValue').textContent = data.transferred;
                }
            }

            updateConnectionStatus(connected) {
                const indicator = document.getElementById('statusIndicator');
                const status = document.getElementById('connectionStatus');
                const connectBtn = document.getElementById('connectBtn');

                if (connected) {
                    indicator.classList.add('connected');
                    status.textContent = 'Connected';
                    connectBtn.textContent = 'Disconnect from Server';
                    connectBtn.classList.remove('btn-secondary');
                    connectBtn.classList.add('btn-primary');
                } else {
                    indicator.classList.remove('connected');
                    status.textContent = 'Disconnected';
                    connectBtn.textContent = 'Connect to Server';
                    connectBtn.classList.remove('btn-primary');
                    connectBtn.classList.add('btn-secondary');
                }
            }

            updatePhase(phase) {
                document.getElementById('phaseIndicator').textContent = phase;
                this.currentPhase = phase;
            }

            setProgress(percentage) {
                const ring = document.getElementById('progressRing');
                const text = document.getElementById('progressText');
                const label = document.getElementById('progressLabel');
                
                const circumference = 2 * Math.PI * 90;
                const offset = circumference - (percentage / 100) * circumference;
                
                ring.style.strokeDashoffset = offset;
                text.textContent = Math.round(percentage) + '%';
                
                if (percentage === 0) {
                    label.textContent = 'Ready';
                } else if (percentage < 100) {
                    label.textContent = 'In Progress';
                } else {
                    label.textContent = 'Complete';
                }
            }

            updateSecurityStatus(statusId, active) {
                const element = document.getElementById(statusId);
                if (active) {
                    element.classList.remove('inactive');
                    element.classList.add('active');
                    element.textContent = 'Active';
                } else {
                    element.classList.remove('active');
                    element.classList.add('inactive');
                    element.textContent = 'Pending';
                }
            }

            addLogEntry(message, type = 'info') {
                const log = document.getElementById('statusLog');
                const entry = document.createElement('div');
                entry.className = 'log-entry';
                
                const timestamp = new Date().toLocaleTimeString();
                entry.innerHTML = `
                    <span class="log-timestamp">${timestamp}</span>
                    <div class="log-status ${type}"></div>
                    <span>${message}</span>
                `;
                
                log.appendChild(entry);
                log.scrollTop = log.scrollHeight;
                
                // Keep only last 50 entries
                if (log.children.length > 50) {
                    log.removeChild(log.firstChild);
                }
            }

            toggleMonitoring() {
                this.isMonitoring = !this.isMonitoring;
                const btn = document.getElementById('monitorBtn');
                
                if (this.isMonitoring) {
                    btn.textContent = 'Live Monitor: ON';
                    btn.classList.remove('btn-secondary');
                    btn.classList.add('btn-primary');
                    this.addLogEntry('Live monitoring resumed', 'info');
                } else {
                    btn.textContent = 'Live Monitor: OFF';
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-secondary');
                    this.addLogEntry('Live monitoring paused', 'info');
                }
            }

            toggleConnection() {
                const connectBtn = document.getElementById('connectBtn');
                const isConnected = connectBtn.textContent.includes('Disconnect');

                if (isConnected) {
                    // Disconnect
                    connectBtn.textContent = 'Connect to Server';
                    connectBtn.classList.remove('btn-primary');
                    connectBtn.classList.add('btn-secondary');
                    this.updateConnectionStatus(false);
                    this.addLogEntry('Manual disconnect requested', 'info');

                    // Create a disconnect command file
                    this.createCommandFile('disconnect');
                } else {
                    // Connect
                    connectBtn.textContent = 'Disconnect from Server';
                    connectBtn.classList.remove('btn-secondary');
                    connectBtn.classList.add('btn-primary');
                    this.addLogEntry('Manual connection requested', 'info');

                    // Create a connect command file
                    this.createCommandFile('connect');
                }
            }

            createCommandFile(command) {
                // Create a command file that the C++ client can read
                const commandData = {
                    command: command,
                    timestamp: new Date().toISOString()
                };

                // Since we can't write files directly from browser, we'll use a different approach
                // The client should check for this in the status monitoring
                this.addLogEntry(`Command: ${command}`, 'info');
            }

            refreshStatus() {
                this.addLogEntry('Status refresh requested', 'info');
                this.lastLineCount = 0; // Force re-read of all status
                this.checkForUpdates();
            }
        }

        // Initialize monitoring when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new StatusMonitor();
        });
    </script>
</body>
</html>