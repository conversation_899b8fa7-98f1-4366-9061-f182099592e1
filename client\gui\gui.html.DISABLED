<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberBackup Pro - v3.0 (API Driven)</title>
    <style>
        /* CRITICAL FIX: Move @import to top */
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Share+Tech+Mono:wght@400&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Enhanced Cyberpunk Color Palette */
            --neon-pink: #FF00FF;
            --neon-blue: #00FFFF;
            --neon-purple: #8A2BE2;
            --neon-green: #00FF00;
            --neon-yellow: #FFFF00;
            --neon-orange: #FF4500;
            --neon-red: #FF0040;
            --electric-blue: #0080FF;
            --cyber-lime: #32CD32;
            --plasma-pink: #FF1493;
            
            --bg-black: #000000;
            --bg-dark: #0A0A0A;
            --bg-medium: #1A1A2E;
            --bg-light: #16213E;
            --bg-card: rgba(26, 26, 46, 0.95);
            --bg-glass: rgba(255, 255, 255, 0.05);
            
            --text-primary: #FFFFFF;
            --text-secondary: #E0E0E0;
            --text-dim: #888888;
            --text-contrast: rgba(0, 0, 0, 0.8);
            
            --success: var(--neon-green);
            --warning: var(--neon-yellow);
            --error: var(--neon-red);
            --info: var(--neon-blue);
            
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-border-active: rgba(0, 255, 255, 0.6);
            
            /* Enhanced Glow Effects */
            --glow-pink: 0 0 10px var(--neon-pink), 0 0 20px var(--neon-pink), 0 0 40px var(--neon-pink);
            --glow-blue: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 40px var(--neon-blue);
            --glow-green: 0 0 10px var(--neon-green), 0 0 20px var(--neon-green), 0 0 40px var(--neon-green);
            --glow-purple: 0 0 10px var(--neon-purple), 0 0 20px var(--neon-purple), 0 0 40px var(--neon-purple);
            
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s ease-out;
            
            /* Improved Spacing System */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            
            /* Enhanced Border Radius System */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        body {
            font-family: 'Orbitron', 'Share Tech Mono', monospace;
            background: var(--bg-black);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            overflow-y: auto; /* Ensure proper scrolling */
            position: relative;
            font-size: 1.125rem; /* Increased base font size */
        }

        /* Enhanced Animated Background */
        .cyber-grid {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                radial-gradient(circle at 20% 50%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
                linear-gradient(var(--neon-pink) 1px, transparent 1px),
                linear-gradient(90deg, var(--neon-blue) 1px, transparent 1px);
            background-size: 
                100% 100%,
                100% 100%,
                100% 100%,
                100px 100px,
                100px 100px;
            opacity: 0.1;
            animation: cyber-grid-flow 30s linear infinite;
        }

        @keyframes cyber-grid-flow {
            0% { 
                transform: translate(0, 0) rotate(0deg);
                background-position: 0 0, 0 0, 0 0, 0 0, 0 0;
            }
            100% { 
                transform: translate(100px, 100px) rotate(0.5deg);
                background-position: 100px 100px, -50px 50px, 150px -100px, 100px 100px, 100px 100px;
            }
        }

        /* Enhanced Neon Text Effects */
        .neon-text {
            position: relative;
            color: var(--text-primary);
            text-shadow: var(--glow-blue);
            animation: neon-flicker 3s ease-in-out infinite alternate;
        }

        .neon-text.pink { 
            color: var(--neon-pink);
            text-shadow: var(--glow-pink);
        }

        .neon-text.green { 
            color: var(--neon-green);
            text-shadow: var(--glow-green);
        }

        .neon-text.purple { 
            color: var(--neon-purple);
            text-shadow: var(--glow-purple);
        }

        @keyframes neon-flicker {
            0%, 19%, 21%, 23%, 25%, 54%, 56%, 100% {
                opacity: 1;
                text-shadow: var(--glow-blue);
            }
            20%, 24%, 55% {        
                opacity: 0.4;
                text-shadow: none;
            }
        }

        /* Enhanced Accessibility: Better contrast backgrounds */
        .text-with-contrast {
            background: var(--text-contrast);
            padding: 2px 6px;
            border-radius: var(--radius-sm);
            backdrop-filter: blur(10px);
        }

        /* IMPROVED CONTAINER - Better Spacing and Responsive Grid */
        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: var(--space-md) var(--space-md) var(--space-xl); /* FIX: Added bottom padding to prevent clipping */
            display: grid;
            grid-template-columns: minmax(300px, 1fr) minmax(400px, 2fr) minmax(350px, 1fr);
            grid-template-rows: auto 1fr auto;
            gap: var(--space-md);
            min-height: 100vh;
            grid-template-areas: 
                "header header header"
                "config main status"
                "debug debug debug";
        }

        /* Enhanced Glass Card with RGB Border Animation and Drop Shadows */
        .glass-card {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 2px solid transparent;
            border-radius: var(--radius-xl);
            padding: var(--space-md);
            position: relative;
            overflow: hidden;
            transition: var(--transition);
            background-clip: padding-box;
            /* FIX: Enhanced depth with more pronounced drop shadows */
            box-shadow: 
                0 8px 24px rgba(0, 0, 0, 0.5),
                0 2px 6px rgba(0, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, 
                var(--neon-pink), var(--neon-blue), var(--neon-purple), 
                var(--neon-green), var(--neon-yellow), var(--neon-pink));
            background-size: 400% 400%;
            border-radius: calc(var(--radius-xl) + 3px);
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s;
            animation: rgb-border-cycle 4s ease-in-out infinite;
        }

        .glass-card:hover::before,
        .glass-card.active::before {
            opacity: 0.7;
        }

        .glass-card:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 8px 24px rgba(0, 0, 0, 0.5),
                0 4px 12px rgba(0, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        @keyframes rgb-border-cycle {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Particle Effect Container */
        .particle-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--neon-blue);
            border-radius: 50%;
            animation: particle-float 3s linear infinite;
        }

        @keyframes particle-float {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) scale(1);
                opacity: 0;
            }
        }

        /* Enhanced Header - Better Layout */
        .header {
            grid-area: header;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-xl);
            border-bottom: 3px solid transparent;
            background: linear-gradient(90deg, var(--bg-dark) 0%, var(--bg-medium) 100%);
            position: relative;
            border-image: linear-gradient(90deg, var(--neon-pink), var(--neon-blue), var(--neon-purple)) 1;
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-md);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-lg);
        }

        .logo-icon {
            font-size: 2.5rem;
            animation: logo-pulse 4s ease-in-out infinite;
            color: var(--neon-pink);
            cursor: pointer;
            transition: var(--transition);
            position: relative;
        }

        .logo-icon:hover {
            transform: scale(1.1);
            filter: drop-shadow(var(--glow-pink));
        }

        .logo-icon:focus {
            outline: 2px solid var(--neon-blue);
            outline-offset: 4px;
        }

        @keyframes logo-pulse {
            0%, 100% { 
                transform: scale(1);
                color: var(--neon-pink);
            }
            25% { 
                transform: scale(1.05);
                color: var(--neon-blue);
            }
            50% { 
                transform: scale(1.1);
                color: var(--neon-purple);
            }
            75% { 
                transform: scale(1.05);
                color: var(--neon-green);
            }
        }

        .logo-text h1 {
            font-size: 2rem;
            font-weight: 900;
            color: var(--neon-pink);
            text-transform: uppercase;
            letter-spacing: 3px;
            margin-bottom: var(--space-xs);
            position: relative;
        }

        .logo-text .subtitle {
            font-size: 0.875rem;
            color: var(--neon-blue);
            text-transform: uppercase;
            letter-spacing: 2px;
            font-family: 'Share Tech Mono', monospace;
        }

        /* Enhanced Connection Status with Icons */
        .connection-status {
            display: flex;
            align-items: center;
            gap: var(--space-lg);
            padding: var(--space-lg) var(--space-xl);
            background: var(--bg-card);
            border-radius: 50px;
            border: 2px solid var(--glass-border);
            transition: var(--transition);
        }

        .connection-status:hover {
            border-color: var(--glass-border-active);
            box-shadow: var(--glow-blue);
        }

        .status-led {
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            background: var(--error);
            position: relative;
            animation: pulse-error 2s infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: bold;
            color: white;
            box-shadow: 
                0 0 10px rgba(255, 0, 64, 0.4),
                0 0 20px rgba(255, 0, 64, 0.2);
        }

        .status-led.connected {
            background: var(--success);
            animation: pulse-success 2s infinite;
            box-shadow: 
                var(--glow-green),
                0 0 15px rgba(0, 255, 0, 0.4);
        }

        .status-led.connecting {
            background: var(--warning);
            animation: pulse-warning 1s infinite;
            box-shadow: 
                0 0 10px rgba(255, 255, 0, 0.4),
                0 0 20px rgba(255, 255, 0, 0.2);
        }

        .status-led.connected::after {
            content: '✓';
        }

        .status-led.connecting::after {
            content: '⟳';
            animation: spin 1s linear infinite;
        }

        .status-led:not(.connected):not(.connecting)::after {
            content: '✗';
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse-success {
            0%, 100% { 
                box-shadow: 
                    var(--glow-green),
                    0 0 15px rgba(0, 255, 0, 0.4);
                transform: scale(1);
            }
            50% { 
                box-shadow: 
                    0 0 30px var(--neon-green), 
                    0 0 60px var(--neon-green),
                    0 0 25px rgba(0, 255, 0, 0.6);
                transform: scale(1.05);
            }
        }

        @keyframes pulse-error {
            0%, 100% { 
                box-shadow: 
                    0 0 10px rgba(255, 0, 64, 0.4),
                    0 0 20px rgba(255, 0, 64, 0.2);
                transform: scale(1);
            }
            50% { 
                box-shadow: 
                    0 0 20px var(--error), 
                    0 0 40px var(--error),
                    0 0 30px rgba(255, 0, 64, 0.4);
                transform: scale(1.05);
            }
        }

        @keyframes pulse-warning {
            0%, 100% { 
                box-shadow: 
                    0 0 10px rgba(255, 255, 0, 0.4),
                    0 0 20px rgba(255, 255, 0, 0.2);
                transform: scale(1);
            }
            50% { 
                box-shadow: 
                    0 0 20px var(--warning), 
                    0 0 40px var(--warning),
                    0 0 30px rgba(255, 255, 0, 0.4);
                transform: scale(1.05);
            }
        }

        /* Enhanced Panel Headers */
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-md);
            border-bottom: 1px solid var(--glass-border);
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--neon-blue);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Configuration Panel Improvements */
        .config-panel {
            grid-area: config;
        }

        .config-section {
            margin-bottom: var(--space-lg);
        }

        .config-label {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--space-sm);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .config-value {
            font-size: 1rem;
            color: var(--text-dim);
            margin-top: var(--space-sm);
            font-family: 'Share Tech Mono', monospace;
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            max-width: 100%;
        }

        .config-value.file-info {
            background: var(--bg-black);
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--radius-md);
            border: 1px solid var(--glass-border);
        }

        .file-icon {
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .file-details {
            flex: 1;
            min-width: 0; /* Allow text to truncate */
        }

        .file-name {
            font-weight: 600;
            color: var(--text-primary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }

        .file-size {
            font-size: 0.875rem;
            color: var(--text-dim);
            margin-top: 2px;
        }

        /* Enhanced Input Fields */
        .config-input {
            width: 100%;
            padding: var(--space-md) var(--space-lg);
            background: var(--bg-black);
            border: 2px solid var(--glass-border);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-family: 'Share Tech Mono', monospace;
            font-size: 1rem;
            transition: var(--transition);
            position: relative;
        }

        .config-input:focus {
            outline: none;
            border-color: var(--neon-blue);
            box-shadow: var(--glow-blue);
            background: rgba(0, 255, 255, 0.05);
        }

        .config-input.error {
            border-color: var(--error);
            box-shadow: 0 0 20px rgba(255, 0, 64, 0.3);
            animation: input-shake 0.5s ease-in-out;
        }

        @keyframes input-shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Enhanced File Drop Zone with Better Visual Feedback */
        .file-drop-zone {
            position: relative;
            margin-bottom: var(--space-lg);
            border: 2px dashed var(--glass-border);
            border-radius: var(--radius-xl);
            padding: var(--space-2xl);
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            background: var(--bg-glass);
            box-shadow: 
                inset 0 2px 6px rgba(0, 0, 0, 0.2),
                0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .file-drop-zone:hover,
        .file-drop-zone.drag-over {
            border-color: var(--neon-blue);
            background: rgba(0, 255, 255, 0.1);
            box-shadow: 
                var(--glow-blue),
                inset 0 2px 6px rgba(0, 255, 255, 0.1),
                0 4px 16px rgba(0, 255, 255, 0.2);
            transform: scale(1.02);
        }

        .file-drop-zone.drag-over::before {
            content: '📁 Drop file here!';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.25rem;
            color: var(--neon-blue);
            font-weight: bold;
            z-index: 10;
            text-shadow: var(--glow-blue);
            animation: pulse-glow 1s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            0% { opacity: 0.8; transform: translate(-50%, -50%) scale(1); }
            100% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-md);
            padding: var(--space-lg);
            background: linear-gradient(45deg, var(--neon-purple), var(--neon-blue));
            border: none;
            border-radius: var(--radius-md);
            color: white;
            font-weight: 700;
            text-transform: uppercase;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            width: 100%;
        }

        .file-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--glow-purple);
            filter: brightness(1.2);
        }

        .file-button:focus {
            outline: 2px solid var(--neon-blue);
            outline-offset: 2px;
        }

        /* Main Panel Improvements */
        .main-panel {
            grid-area: main;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .phase-display {
            margin-bottom: var(--space-xl);
            font-size: 1.375rem;
            font-weight: 700;
            text-align: center;
        }

        /* Enhanced Progress Ring with Better Proportions */
        .progress-container {
            position: relative;
            width: 300px;
            height: 300px;
            margin-bottom: var(--space-2xl);
        }

        /* Enhanced Progress Ring with Dynamic Glow */
        .progress-ring {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
            filter: drop-shadow(var(--glow-blue));
            transition: filter 0.3s ease;
        }

        .progress-ring.active {
            filter: drop-shadow(0 0 20px var(--neon-blue)) drop-shadow(0 0 40px var(--neon-blue));
        }

        .progress-fill {
            fill: none;
            stroke: url(#progressGradient);
            stroke-width: 20;
            stroke-linecap: round;
            transition: stroke-dashoffset 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .progress-percentage {
            font-size: 2.75rem;
            font-weight: 900;
            color: var(--neon-blue);
            margin-bottom: var(--space-sm);
            text-shadow: var(--glow-blue);
        }

        .progress-label {
            font-size: 1rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Enhanced Control Panel */
        .control-panel {
            display: flex;
            gap: var(--space-md);
            margin-bottom: var(--space-2xl);
            flex-wrap: wrap;
            justify-content: center;
        }

        /* Unified Cyberpunk Button System - Enhanced */
        .cyber-btn {
            padding: var(--space-md) var(--space-lg);
            border: 2px solid;
            background: transparent;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 700;
            text-transform: uppercase;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            letter-spacing: 1px;
            font-family: 'Orbitron', monospace;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-sm);
            min-height: 44px; /* Better touch targets */
        }

        .cyber-btn:focus {
            outline: 2px solid var(--neon-blue);
            outline-offset: 2px;
        }

        .cyber-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .cyber-btn.primary {
            border-color: var(--neon-blue);
            color: var(--neon-blue);
        }

        .cyber-btn.primary:hover:not(:disabled) {
            background: var(--neon-blue);
            color: var(--bg-black);
            box-shadow: var(--glow-blue);
            transform: translateY(-2px);
        }

        .cyber-btn.success {
            border-color: var(--neon-green);
            color: var(--neon-green);
        }

        .cyber-btn.success:hover:not(:disabled) {
            background: var(--neon-green);
            color: var(--bg-black);
            box-shadow: var(--glow-green);
            transform: translateY(-2px);
        }

        .cyber-btn.danger {
            border-color: var(--error);
            color: var(--error);
        }

        .cyber-btn.danger:hover:not(:disabled) {
            background: var(--error);
            color: var(--bg-black);
            box-shadow: 0 0 20px rgba(255, 0, 64, 0.6);
            transform: translateY(-2px);
        }

        .cyber-btn.secondary {
            border-color: var(--neon-purple);
            color: var(--neon-purple);
        }

        .cyber-btn.secondary:hover:not(:disabled) {
            background: var(--neon-purple);
            color: var(--bg-black);
            box-shadow: var(--glow-purple);
            transform: translateY(-2px);
        }

        /* Large Control Buttons */
        .cyber-btn.large {
            padding: var(--space-lg) var(--space-xl);
            font-size: 1rem;
            min-width: 160px;
        }

        .cyber-btn.large:hover:not(:disabled) {
            transform: translateY(-3px) scale(1.02);
        }

        /* Small compact buttons */
        .cyber-btn.small {
            padding: var(--space-sm) var(--space-md);
            font-size: 0.625rem;
            min-width: auto;
        }

        .control-btn {
            /* Extend cyber-btn for control buttons */
        }

        .btn-group {
            display: flex;
            gap: var(--space-sm);
            flex-wrap: wrap;
            margin-top: var(--space-lg);
        }

        /* Enhanced Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--space-md);
            width: 100%;
            max-width: 600px;
        }

        .stat-card {
            background: var(--bg-black);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: var(--space-lg);
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 3px 8px rgba(0, 0, 0, 0.3),
                0 1px 4px rgba(0, 255, 255, 0.1);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .stat-card:hover {
            border-color: var(--neon-blue);
            box-shadow: 
                0 6px 16px rgba(0, 0, 0, 0.4),
                0 2px 8px rgba(0, 255, 255, 0.2),
                var(--glow-blue);
            transform: translateY(-8px) scale(1.02);
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-value {
            font-size: 1.375rem;
            font-weight: 700;
            color: var(--neon-blue);
            margin-bottom: var(--space-sm);
            font-family: 'Share Tech Mono', monospace;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Status Panel Improvements */
        .status-panel {
            grid-area: status;
        }

        .log-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-md);
            padding: var(--space-md);
            background: var(--bg-black);
        }

        /* Enhanced Security Badges */
        .security-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-sm);
            margin-top: var(--space-lg);
        }

        .security-badge {
            padding: var(--space-lg);
            background: var(--bg-black);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            gap: var(--space-md);
            transition: var(--transition);
            position: relative;
            box-shadow: 
                0 2px 6px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }

        .security-badge.active {
            border-color: var(--success);
            box-shadow: inset 0 0 20px rgba(0, 255, 0, 0.2);
            background: rgba(0, 255, 0, 0.05);
            animation: security-activation 0.6s ease-out;
        }

        @keyframes security-activation {
            0% {
                transform: scale(0.8);
                opacity: 0;
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .security-badge.active .badge-icon {
            color: var(--success);
            animation: security-pulse 2s infinite;
            filter: drop-shadow(var(--glow-green));
        }

        @keyframes security-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .badge-icon {
            font-size: 1.25rem;
        }

        .badge-text {
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Theme Selector Improvements */
        .theme-selector {
            display: flex;
            gap: var(--space-md);
            justify-content: center;
            align-items: center;
            margin-top: var(--space-lg);
        }

        .theme-btn {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            border: 3px solid var(--glass-border);
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            outline: none;
        }

        .theme-btn:focus {
            outline: 2px solid var(--neon-blue);
            outline-offset: 3px;
        }

        .theme-btn.cyberpunk {
            background: linear-gradient(45deg, var(--neon-pink), var(--neon-blue), var(--neon-purple));
        }

        .theme-btn.dark {
            background: linear-gradient(45deg, #2C3E50, #34495E, #1A1A1A);
        }

        .theme-btn.matrix {
            background: linear-gradient(45deg, #000000, var(--neon-green), #003300);
        }

        .theme-btn:hover {
            transform: scale(1.15);
            box-shadow: 0 0 25px currentColor;
            border-color: var(--neon-blue);
        }

        .theme-btn.active {
            border-color: var(--neon-blue);
            box-shadow: var(--glow-blue);
            transform: scale(1.1);
        }

        .theme-btn.active::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 1rem;
            text-shadow: 0 0 10px rgba(0,0,0,0.8);
        }

        /* Enhanced Log System */
        .log-entry {
            padding: var(--space-md);
            margin-bottom: var(--space-sm);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-family: 'Share Tech Mono', monospace;
            display: flex;
            align-items: flex-start;
            gap: var(--space-md);
            transition: var(--transition);
            cursor: pointer;
            border-left: 3px solid transparent;
            position: relative;
        }

        .log-entry:hover {
            background: var(--bg-medium);
            border-left-color: var(--neon-blue);
            transform: translateX(5px);
        }

        .log-entry:focus {
            outline: 2px solid var(--neon-blue);
            outline-offset: 2px;
        }

        .log-timestamp {
            font-size: 0.75rem;
            color: var(--text-dim);
            min-width: 60px;
        }

        .log-icon {
            font-size: 0.875rem;
            min-width: 16px;
        }

        .log-icon.success {
            color: var(--success);
        }

        .log-icon.error {
            color: var(--error);
        }

        .log-icon.info {
            color: var(--info);
        }

        .log-content {
            flex: 1;
        }

        .log-message {
            font-weight: 600;
            margin-bottom: var(--space-xs);
        }

        .log-details {
            font-size: 0.75rem;
            color: var(--text-dim);
            opacity: 0.8;
        }

        /* Enhanced Toast System with Better Stacking */
        .toast-container {
            position: fixed;
            top: var(--space-lg);
            right: var(--space-lg);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: var(--space-md);
            max-width: 400px;
        }

        .toast {
            background: var(--bg-card);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            padding: var(--space-lg) var(--space-xl);
            animation: toast-slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(20px);
            box-shadow: 
                0 4px 12px rgba(0, 0, 0, 0.5),
                0 2px 6px rgba(0, 0, 0, 0.3);
            min-width: 300px;
        }

        .toast.success {
            border-left: 4px solid var(--success);
            box-shadow: var(--glow-green);
        }

        .toast.error {
            border-left: 4px solid var(--error);
            box-shadow: 0 0 20px rgba(255, 0, 64, 0.3);
        }

        .toast.info {
            border-left: 4px solid var(--info);
            box-shadow: var(--glow-blue);
        }

        @keyframes toast-slide-in {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes toast-slide-out {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .toast-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--space-md);
        }

        .toast-message {
            flex: 1;
            line-height: 1.4;
        }

        .toast-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-sm);
            transition: var(--transition);
            flex-shrink: 0;
        }

        .toast-close:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        /* Enhanced Custom Modal System */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(15px);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay.show {
            display: flex;
            animation: modal-fade-in 0.3s ease-out;
        }

        @keyframes modal-fade-in {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal {
            background: var(--bg-card);
            border: 2px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: var(--space-2xl);
            max-width: 500px;
            width: 90%;
            position: relative;
            animation: modal-slide-up 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
        }

        @keyframes modal-slide-up {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-md);
            border-bottom: 1px solid var(--glass-border);
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--neon-blue);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: var(--space-sm);
            border-radius: var(--radius-sm);
            transition: var(--transition);
        }

        .modal-close:hover {
            color: var(--neon-red);
            background: rgba(255, 0, 64, 0.1);
        }

        .confirm-modal .modal {
            max-width: 400px;
        }

        .confirm-modal .modal-title {
            color: var(--neon-yellow);
            margin-bottom: var(--space-lg);
        }

        .confirm-modal .modal-message {
            color: var(--text-secondary);
            margin-bottom: var(--space-xl);
            line-height: 1.5;
        }

        .confirm-modal .modal-buttons {
            display: flex;
            gap: var(--space-lg);
            justify-content: flex-end;
        }

        /* Debug Panel Improvements */
        .debug-panel {
            grid-area: debug;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            margin-bottom: var(--space-lg); /* Ensure spacing from bottom */
        }

        .debug-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .debug-content.show {
            max-height: 500px;
        }

        .debug-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-lg);
            margin-bottom: var(--space-lg);
        }

        .debug-section h3 {
            color: var(--neon-purple);
            margin-bottom: var(--space-md);
            font-size: 1.125rem;
        }

        .json-viewer {
            background: var(--bg-black);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-md);
            padding: var(--space-md);
            font-family: 'Share Tech Mono', monospace;
            font-size: 0.875rem;
            color: var(--text-secondary);
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        /* Enhanced Mobile Responsiveness */
        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-areas: 
                    "header"
                    "main"
                    "config"
                    "status"
                    "debug";
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .debug-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: var(--space-md) var(--space-md) var(--space-2xl); /* Extra bottom padding on mobile */
                gap: var(--space-md);
            }

            .header {
                padding: var(--space-lg);
                flex-direction: column;
                gap: var(--space-md);
            }

            .logo-icon {
                font-size: 2rem;
            }

            .logo-text h1 {
                font-size: 1.75rem;
            }

            .progress-container {
                width: 250px;
                height: 250px;
            }

            .progress-percentage {
                font-size: 2.25rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .control-panel {
                flex-direction: column;
                align-items: center;
            }

            .cyber-btn.large {
                width: 100%;
                max-width: 250px;
            }

            .security-grid {
                grid-template-columns: 1fr;
            }

            .theme-selector {
                gap: var(--space-md);
            }

            .btn-group {
                flex-direction: column;
            }

            .toast-container {
                top: var(--space-md);
                right: var(--space-md);
                left: var(--space-md);
                max-width: none;
            }

            .toast {
                min-width: auto;
                width: 100%;
            }

            .debug-panel {
                margin-bottom: var(--space-2xl);
            }
        }

        /* Loading States */
        .loading {
            display: inline-block;
            animation: loading-pulse 1.5s infinite;
        }

        @keyframes loading-pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        .loading-spinner {
            display: inline-block;
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid var(--glass-border);
            border-radius: 50%;
            border-top-color: var(--neon-blue);
            animation: spin 1s ease-in-out infinite;
        }

        /* Accessibility Improvements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        [aria-live] {
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        }

        /* Focus management */
        *:focus {
            outline: 2px solid var(--neon-blue);
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --glass-border: rgba(255, 255, 255, 0.8);
                --text-secondary: #FFFFFF;
                --text-dim: #CCCCCC;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <div class="cyber-grid"></div>
    <div class="particle-container" id="particleContainer"></div>
    <div class="toast-container" id="toastContainer" role="status" aria-live="polite"></div>
    <div aria-live="polite" aria-atomic="true" class="sr-only" id="announcements"></div>
    
    <div class="container">
        <!-- Header -->
        <header class="header" role="banner">
            <div class="logo">
                <div class="logo-icon neon-text pink" tabindex="0" role="button" aria-label="CyberBackup Logo">⚡</div>
                <div class="logo-text">
                    <h1 class="neon-text pink">CYBERBACKUP PRO</h1>
                    <div class="subtitle">v3.0 (API Driven)</div>
                </div>
            </div>
            <div class="connection-status">
                <div class="status-led" id="connectionLed" aria-label="Connection status"></div>
                <span id="connectionText" style="font-weight: 600;">OFFLINE</span>
            </div>
        </header>

        <!-- Configuration Panel -->
        <aside class="config-panel glass-card" role="region" aria-label="Configuration">
            <div class="panel-header">
                <h2 class="panel-title">System Configuration</h2>
            </div>
            
            <div class="config-section">
                <label class="config-label" for="serverInput">Server Address</label>
                <input type="text" class="config-input" id="serverInput" 
                       placeholder="127.0.0.1:1256" aria-describedby="serverHelp">
                <div id="serverHelp" class="sr-only">Enter server IP address and port</div>
            </div>
            
            <div class="config-section">
                <label class="config-label" for="usernameInput">Username</label>
                <input type="text" class="config-input" id="usernameInput" 
                       placeholder="Enter username" maxlength="100" aria-describedby="usernameHelp">
                <div id="usernameHelp" class="sr-only">Username for authentication (max 100 characters)</div>
            </div>
            
            <div class="config-section">
                <label class="config-label">Target File</label>
                <div class="file-drop-zone" id="fileDropZone">
                    <input type="file" class="file-input" id="fileInput" 
                           aria-describedby="fileHelp" accept="*/*">
                    <div class="file-button">
                        📁 Select File to Backup
                        <span class="sr-only">or drag and drop a file here</span>
                    </div>
                </div>
                <div id="fileHelp" class="sr-only">Select a file for backup or drag and drop</div>
                <div class="config-value" id="selectedFile">No file selected</div>
            </div>
            
            <div class="config-section">
                <label class="config-label">Client ID</label>
                <div class="config-value" id="clientId">Not connected</div>
            </div>

            <div class="btn-group">
                <button class="cyber-btn success" onclick="gui.saveConfiguration()">💾 Save Config</button>
            </div>

            <div class="panel-header" style="margin-top: var(--space-xl);">
                <h2 class="panel-title">Theme Selection</h2>
            </div>
            
            <div class="theme-selector">
                <button class="theme-btn cyberpunk active" onclick="gui.setTheme('cyberpunk')" 
                        aria-label="Cyberpunk theme" title="Cyberpunk Theme"></button>
                <button class="theme-btn dark" onclick="gui.setTheme('dark')" 
                        aria-label="Dark theme" title="Dark Theme"></button>
                <button class="theme-btn matrix" onclick="gui.setTheme('matrix')" 
                        aria-label="Matrix theme" title="Matrix Theme"></button>
            </div>
        </aside>

        <!-- Main Panel -->
        <main class="main-panel glass-card" role="main">
            <div class="phase-display" id="currentPhase">
                <span class="neon-text purple">SYSTEM READY</span>
            </div>
            
            <div class="progress-container">
                <svg class="progress-ring" viewBox="0 0 300 300" role="img" aria-label="Transfer progress">
                    <defs>
                        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:var(--neon-blue);stop-opacity:1" />
                            <stop offset="33%" style="stop-color:var(--neon-purple);stop-opacity:1" />
                            <stop offset="66%" style="stop-color:var(--neon-pink);stop-opacity:1" />
                            <stop offset="100%" style="stop-color:var(--neon-green);stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <circle class="progress-bg" cx="150" cy="150" r="130" 
                            fill="none" stroke="var(--bg-light)" stroke-width="16"></circle>
                    <circle class="progress-fill" cx="150" cy="150" r="130" id="progressCircle"></circle>
                </svg>
                <div class="progress-text">
                    <div class="progress-percentage neon-text" id="progressPercentage">0%</div>
                    <div class="progress-label" id="progressStatus">READY</div>
                </div>
            </div>
            
            <div class="control-panel">
                <button class="cyber-btn primary large control-btn" id="primaryAction" onclick="gui.handlePrimaryAction()">
                    <span class="btn-icon">🚀</span>
                    <span class="btn-text">CONNECT</span>
                </button>
                <button class="cyber-btn secondary large control-btn" id="pauseBtn" onclick="gui.pauseOperation()" disabled>
                    <span class="btn-icon">⏸️</span>
                    <span class="btn-text">PAUSE</span>
                </button>
                <button class="cyber-btn danger large control-btn" id="stopBtn" onclick="gui.stopOperation()" disabled>
                    <span class="btn-icon">🛑</span>
                    <span class="btn-text">STOP</span>
                </button>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="speedStat">0 B/s</div>
                    <div class="stat-label">Transfer Speed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="etaStat">--:--</div>
                    <div class="stat-label">ETA</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="transferredStat">0 B</div>
                    <div class="stat-label">Transferred</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="sizeStat">0 B</div>
                    <div class="stat-label">Total Size</div>
                </div>
            </div>
        </main>

        <!-- Status Panel -->
        <aside class="status-panel glass-card" role="region" aria-label="Operation log">
            <div class="panel-header">
                <h2 class="panel-title">Operation Log</h2>
                <div class="btn-group">
                    <button class="cyber-btn success small" onclick="gui.exportLog()">📥 Export</button>
                    <button class="cyber-btn danger small" onclick="gui.clearLog()">🗑️ Clear</button>
                    <button class="cyber-btn primary small" onclick="gui.toggleAutoScroll()" id="autoScrollBtn">
                        📜 Auto-scroll: ON
                    </button>
                </div>
            </div>
            
            <div class="log-container" id="logContainer" role="log" aria-live="polite" aria-label="Activity log">
                <!-- Logs will be populated here -->
            </div>
        </aside>

        <!-- Debug Panel -->
        <div class="debug-panel glass-card" role="region" aria-label="Developer console">
            <div class="panel-header">
                <h2 class="panel-title">Developer Console</h2>
                <button class="cyber-btn primary small" onclick="gui.toggleDebug()" id="debugToggle" 
                        aria-expanded="false">🔧 Toggle Debug</button>
            </div>
            
            <div class="debug-content" id="debugContent">
                <div class="debug-grid">
                    <div class="debug-section">
                        <h3>📊 Last API Response</h3>
                        <div class="json-viewer" id="statusJsonViewer">Waiting for status data...</div>
                    </div>
                    <div class="debug-section">
                        <h3>📈 State Machine</h3>
                        <div class="json-viewer" id="progressJsonViewer">Waiting for state data...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Custom Confirmation Modal -->
    <div class="modal-overlay confirm-modal" id="confirmModal">
        <div class="modal">
            <h3 class="modal-title" id="confirmTitle">Confirm Action</h3>
            <p class="modal-message" id="confirmMessage">Are you sure?</p>
            <div class="modal-buttons">
                <button class="cyber-btn secondary" onclick="gui.hideConfirmModal()">Cancel</button>
                <button class="cyber-btn danger" id="confirmOkBtn" onclick="gui.confirmAction()">Confirm</button>
            </div>
        </div>
    </div>
    
    <script>
        /**
         * =================================================================
         * API-DRIVEN CLIENT GUI v3.0
         * =================================================================
         * This version has been refactored to communicate with a local
         * C++ web server (via Boost.Beast) instead of a file-based system.
         * The GUI now acts as a true frontend to the C++ backend.
         * =================================================================
         */

        // Interval Manager to prevent leaks
        class IntervalManager {
            constructor() {
                this.intervals = new Map();
            }
            set(name, callback, delay) {
                this.clear(name);
                this.intervals.set(name, setInterval(callback, delay));
            }
            clear(name) {
                if (this.intervals.has(name)) {
                    clearInterval(this.intervals.get(name));
                    this.intervals.delete(name);
                }
            }
            clearAll() {
                this.intervals.forEach(id => clearInterval(id));
                this.intervals.clear();
            }
        }

        /**
         * NEW: ApiClient
         * Handles all communication with the local C++ web server.
         */
        class ApiClient {
            constructor(baseUrl = 'http://127.0.0.1:9090') {
                this.baseUrl = baseUrl;
            }

            async _request(endpoint, options = {}) {
                try {
                    const response = await fetch(`${this.baseUrl}${endpoint}`, options);
                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({ message: response.statusText }));
                        throw new Error(`API Error: ${errorData.message || 'Unknown Error'}`);
                    }
                    return await response.json();
                } catch (err) {
                    // This catches network errors (e.g., C++ client not running) or API errors
                    console.error(`API request to ${endpoint} failed:`, err);
                    throw err; // Re-throw to be handled by the calling function
                }
            }

            getStatus() {
                return this._request('/api/status');
            }

            connect(config) {
                return this._request('/api/connect', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
            }

            startBackup(fileInfo) {
                return this._request('/api/start_backup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(fileInfo)
                });
            }

            stop() { return this._request('/api/stop', { method: 'POST' }); }
            pause() { return this._request('/api/pause', { method: 'POST' }); }
            resume() { return this._request('/api/resume', { method: 'POST' }); }
        }

        // Configuration Manager (largely unchanged)
        class ConfigManager {
            constructor() {
                this.config = {
                    server: '127.0.0.1',
                    port: 1256,
                    username: '',
                    filepath: '',
                };
            }
            loadFromStorage() {
                try {
                    const saved = localStorage.getItem('cyberbackup_config');
                    if (saved) {
                        this.config = { ...this.config, ...JSON.parse(saved) };
                    }
                } catch (error) {
                    console.warn('Invalid config in localStorage.');
                    localStorage.removeItem('cyberbackup_config');
                }
            }
            saveToStorage() {
                localStorage.setItem('cyberbackup_config', JSON.stringify(this.config));
            }
            validate() {
                const errors = [];
                if (!this.config.server || !this.config.port) errors.push('Server address and port are required');
                if (!this.config.username) errors.push('Username is required');
                if (!this.config.filepath) errors.push('A file must be selected');
                return errors;
            }
        }

        // Main Application Class (Refactored for API communication)
        class CyberBackupPro {
            constructor() {
                this.api = new ApiClient();
                this.config = new ConfigManager();
                this.intervals = new IntervalManager();
                // ... (UI and Theme managers are fine as they are)

                this.state = {
                    isConnecting: false, // NEW: Tracks the connection attempt itself
                    isConnected: false,  // Is the C++ client connected to the remote server
                    isRunning: false,
                    isPaused: false,
                    clientId: null,
                    currentPhase: 'SYSTEM_READY',
                    logs: [],
                    progress: {},
                    debugVisible: false,
                    autoScroll: true,
                    selectedFile: null
                };
                
                this.pendingConfirmAction = null;
                
                this.init();
            }
            
            async init() {
                console.log('🚀 CyberBackup Pro v3.0 (API Driven) Initializing...');
                this.config.loadFromStorage();
                this.updateConfigDisplay();
                this.setupEventListeners();
                this.ui = new UIController(); // assuming UIController exists
                this.theme = new ThemeManager(); // assuming ThemeManager exists
                this.theme.loadSavedTheme();

                // Start master status polling
                this.intervals.set('statusPoll', () => this.pollStatus(), 1000);
                this.addLog('System Initialized', 'success', 'Ready to connect to C++ client.');
            }

            setupEventListeners() {
                 // Configuration inputs with validation
                document.getElementById('serverInput').addEventListener('input', (e) => {
                    this.updateServerConfig(e.target.value);
                    this.validateAndUpdateUI();
                });
                
                document.getElementById('usernameInput').addEventListener('input', (e) => {
                    this.config.config.username = e.target.value;
                    this.validateAndUpdateUI();
                });
                
                // Enhanced file input with drag & drop
                const fileInput = document.getElementById('fileInput');
                const dropZone = document.getElementById('fileDropZone');
                
                fileInput.addEventListener('change', (e) => this.handleFileSelection(e));
                
                // Drag & drop functionality
                dropZone.addEventListener('dragover', (e) => { e.preventDefault(); dropZone.classList.add('drag-over'); });
                dropZone.addEventListener('dragleave', (e) => { e.preventDefault(); dropZone.classList.remove('drag-over'); });
                dropZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    dropZone.classList.remove('drag-over');
                    if (e.dataTransfer.files.length > 0) {
                        this.handleFileSelection({ target: { files: e.dataTransfer.files } });
                    }
                });
            }
            
            // NEW: Master Polling Function
            async pollStatus() {
                try {
                    const status = await this.api.getStatus();
                    
                    // Update state machine
                    this.state.isConnected = status.isConnected;
                    this.state.isRunning = status.isRunning;
                    this.state.isPaused = status.isPaused;
                    this.state.currentPhase = status.phase;
                    this.state.clientId = status.clientId;

                    if (status.progress) this.updateProgress(status.progress);
                    if (status.log) this.addLog(status.log.operation, status.log.success ? 'success' : 'error', status.log.details);
                    
                    // The connection attempt succeeded if we get a valid poll response
                    if (this.state.isConnecting) this.state.isConnecting = false;

                } catch (error) {
                    // This error means the C++ client is likely offline
                    this.state.isConnecting = false;
                    this.state.isConnected = false;
                    this.state.isRunning = false;
                }
                
                // Update all UI elements based on the new state
                this.updateAllUI();
            }

            // NEW: Central UI Update Function
            updateAllUI() {
                this.updateConnectionStatus();
                this.updateControlButtons();
                this.updatePhase(this.state.currentPhase);
                this.updateClientIdDisplay();
                if (this.state.debugVisible) {
                    document.getElementById('progressJsonViewer').textContent = JSON.stringify(this.state, null, 2);
                }
            }

            // REFACTORED: Action Handlers now call the API
            async handlePrimaryAction() {
                if (!this.validateAndUpdateUI()) return;
                
                if (this.state.isRunning && this.state.isPaused) await this.resumeOperation();
                else if (!this.state.isConnected) await this.connectToServer();
                else if (!this.state.isRunning) await this.startBackup();
            }

            async connectToServer() {
                this.state.isConnecting = true;
                this.updateAllUI(); // Immediately show "Connecting..."
                try {
                    await this.api.connect(this.config.config);
                    // No need to do anything here, the next pollStatus will update the state
                    this.addLog('Connect command sent', 'info', `Awaiting response from ${this.config.config.server}`);
                } catch (error) {
                    this.state.isConnecting = false;
                    this.updateAllUI();
                    this.showToast('Failed to send connect command. Is the C++ client running?', 'error');
                }
            }

            async startBackup() {
                try {
                    await this.api.startBackup({
                        filename: this.state.selectedFile.name,
                        size: this.state.selectedFile.size,
                        type: this.state.selectedFile.type
                    });
                    this.addLog('Start backup command sent', 'success');
                } catch (error) {
                    this.showToast('Failed to start backup.', 'error');
                }
            }
            
            async pauseOperation() { try { await this.api.pause(); } catch(e) { this.showToast('Failed to send pause command', 'error'); } }
            async resumeOperation() { try { await this.api.resume(); } catch(e) { this.showToast('Failed to send resume command', 'error'); } }
            
            stopOperation() {
                this.showConfirmModal('Stop Operation', 'Are you sure?', () => this.executeStop());
            }

            async executeStop() {
                try {
                    await this.api.stop();
                } catch (e) {
                    this.showToast('Failed to send stop command', 'error');
                }
            }

            // REFACTORED: UI Update Functions now read from this.state
            updateConnectionStatus() {
                const led = document.getElementById('connectionLed');
                const text = document.getElementById('connectionText');
                led.classList.remove('connected', 'connecting');

                if (this.state.isConnecting) {
                    led.classList.add('connecting');
                    text.textContent = 'CONNECTING...';
                } else if (this.state.isConnected) {
                    led.classList.add('connected');
                    text.textContent = 'ONLINE';
                } else {
                    text.textContent = 'OFFLINE';
                }
            }

            updateControlButtons() {
                const primaryBtn = document.getElementById('primaryAction');
                const pauseBtn = document.getElementById('pauseBtn');
                const stopBtn = document.getElementById('stopBtn');
                
                const primaryIcon = primaryBtn.querySelector('.btn-icon');
                const primaryText = primaryBtn.querySelector('.btn-text');
                
                primaryBtn.disabled = false; // Enable by default, disable in specific cases
                pauseBtn.disabled = true;
                stopBtn.disabled = true;

                if (this.state.isConnecting) {
                    primaryBtn.disabled = true;
                    primaryText.textContent = 'CONNECTING';
                } else if (this.state.isRunning) {
                    if (this.state.isPaused) {
                        primaryIcon.textContent = '▶️';
                        primaryText.textContent = 'RESUME';
                    } else {
                        primaryBtn.disabled = true;
                        primaryIcon.textContent = '🔗';
                        primaryText.textContent = 'TRANSFERRING';
                        pauseBtn.disabled = false;
                    }
                    stopBtn.disabled = false;
                } else if (this.state.isConnected) {
                    primaryIcon.textContent = '🚀';
                    primaryText.textContent = 'START BACKUP';
                    if (!this.state.selectedFile) primaryBtn.disabled = true;
                    stopBtn.disabled = false;
                } else { // Offline
                    primaryIcon.textContent = '🚀';
                    primaryText.textContent = 'CONNECT';
                    if (!this.validateAndUpdateUI()) primaryBtn.disabled = true;
                }

                // Lock/unlock config panel
                const panel = document.querySelector('.config-panel');
                const inputs = panel.querySelectorAll('input, button');
                const isBusy = this.state.isConnecting || this.state.isRunning;
                panel.style.opacity = isBusy ? '0.6' : '1';
                inputs.forEach(input => input.disabled = isBusy);
            }

            updateProgress(data) {
                // This function is now just for rendering progress data from the state
                this.state.progress = data;
                const percentage = data.percentage || 0;
                this.ui.updateProgressRing(percentage);
                document.getElementById('progressPercentage').textContent = Math.round(percentage) + '%';
                document.getElementById('speedStat').textContent = data.speed || '0 B/s';
                document.getElementById('etaStat').textContent = data.eta || '--:--';
                document.getElementById('transferredStat').textContent = this.formatBytes(data.transferred || 0);
            }
            
            updateClientIdDisplay() {
                const clientIdEl = document.getElementById('clientId');
                if (this.state.clientId) {
                    clientIdEl.textContent = this.state.clientId.substring(0, 8) + '...';
                    clientIdEl.title = this.state.clientId;
                } else {
                    clientIdEl.textContent = 'Not connected';
                }
            }
            
            // Other functions (logging, modals, etc.) remain largely the same...
            addLog(operation, type, details) {
                const timestamp = new Date().toLocaleTimeString();
                const log = { timestamp, operation, type, details, id: Date.now() + Math.random() };
                this.state.logs.push(log);
                const logContainer = document.getElementById('logContainer');
                const entry = this.ui.createLogEntry(log); // Assumes UIController.createLogEntry exists
                logContainer.appendChild(entry);
                if (this.state.autoScroll) {
                    logContainer.scrollTop = logContainer.scrollHeight;
                }
                if (logContainer.children.length > 200) {
                    logContainer.removeChild(logContainer.firstChild);
                }
            }

            showToast(message, type = 'info') {
                const container = document.getElementById('toastContainer');
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.innerHTML = `<div class="toast-content"><div class="toast-message">${message}</div><button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button></div>`;
                container.appendChild(toast);
                setTimeout(() => {
                    if (container.contains(toast)) {
                        toast.style.animation = 'toast-slide-out 0.3s forwards';
                        setTimeout(() => toast.remove(), 300);
                    }
                }, 5000);
            }

            formatBytes(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

             // ... [The rest of the helper functions: clearLog, exportLog, modal handlers, file handlers, etc.]
            updateServerConfig(value) {
                const parts = value.split(':');
                this.config.config.server = parts[0] || '';
                this.config.config.port = parseInt(parts[1]) || 1256;
            }

            validateAndUpdateUI() {
                const errors = this.config.validate();
                if (errors.length > 0) {
                    // Optionally show errors, but the button state is handled by updateControlButtons
                    return false;
                }
                return true;
            }

            handleFileSelection(event) {
                const file = event.target.files[0];
                if (!file) return;
                this.state.selectedFile = file;
                this.config.config.filepath = file.name; // For saving config
                const sizeFormatted = this.formatBytes(file.size);
                document.getElementById('selectedFile').innerHTML = `<span class="file-icon">📁</span><div class="file-details"><div class="file-name" title="${file.name}">${file.name}</div><div class="file-size">${sizeFormatted}</div></div>`;
                document.getElementById('sizeStat').textContent = sizeFormatted;
                this.addLog('File selected', 'info', `${file.name}`);
                this.updateAllUI();
            }

            saveConfiguration() {
                this.config.saveToStorage();
                this.showToast('Configuration saved to browser storage.', 'success');
            }

            showConfirmModal(title, message, onConfirm) {
                this.pendingConfirmAction = onConfirm;
                document.getElementById('confirmTitle').textContent = title;
                document.getElementById('confirmMessage').textContent = message;
                document.getElementById('confirmModal').classList.add('show');
            }

            hideConfirmModal() {
                document.getElementById('confirmModal').classList.remove('show');
            }

            confirmAction() {
                if(this.pendingConfirmAction) this.pendingConfirmAction();
                this.hideConfirmModal();
            }

            updatePhase(phase) {
                this.state.currentPhase = phase;
                document.getElementById('currentPhase').innerHTML = `<span class="neon-text purple">${phase.replace(/_/g, ' ')}</span>`;
            }
        }
        
        // Dummy UI/Theme controllers to prevent errors if they are not defined elsewhere
        // In a real refactor, these would also be properly modularized.
        if (typeof UIController === 'undefined') {
            class UIController {
                createLogEntry(log) { 
                    const entry = document.createElement('div');
                    entry.className = `log-entry ${log.type}`;
                    const icon = log.type === 'success' ? '✓' : log.type === 'error' ? '✗' : 'ℹ';
                    entry.innerHTML = `<span class="log-timestamp">${log.timestamp}</span><div class="log-icon ${log.type}">${icon}</div><div class="log-content"><div class="log-message">${log.operation}</div><div class="log-details">${log.details || ''}</div></div>`;
                    return entry;
                 }
                updateProgressRing(p) { 
                    const circle = document.getElementById('progressCircle');
                    if (!circle.getAttribute('data-circumference')) {
                        const radius = parseFloat(circle.getAttribute('r'));
                        circle.setAttribute('data-circumference', 2 * Math.PI * radius);
                    }
                    const circumference = circle.getAttribute('data-circumference');
                    circle.style.strokeDasharray = circumference;
                    circle.style.strokeDashoffset = circumference - (p / 100) * circumference;
                }
            }
            window.UIController = UIController;
        }
        if (typeof ThemeManager === 'undefined') {
            class ThemeManager { loadSavedTheme() {} setTheme(t) { console.log(`Theme set to ${t}`) } }
            window.ThemeManager = ThemeManager;
        }
        
        // Initialize the GUI
        const gui = new CyberBackupPro();
    </script>
</body>
</html>